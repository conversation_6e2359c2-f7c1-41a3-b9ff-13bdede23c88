#!/usr/bin/env tsx

/**
 * 为 SearchConfig 表添加详细注释的 Next.js 脚本
 * 
 * 此脚本通过 Next.js 的数据库连接直接执行 SQL 语句来添加表和字段注释，
 * 不使用 Prisma 迁移，避免清空现有的配置表格和业务表格。
 * 
 * 使用方法：
 * npm run tsx scripts/add-searchconfig-comments.ts
 * 或
 * npx tsx scripts/add-searchconfig-comments.ts
 */

import { db } from '../src/lib/prisma';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 读取并执行 SQL 文件
 */
async function executeSqlFile(filePath: string): Promise<void> {
  try {
    console.log(`📖 读取 SQL 文件: ${filePath}`);
    
    // 读取 SQL 文件内容
    const sqlContent = fs.readFileSync(filePath, 'utf-8');
    
    // 按分号分割 SQL 语句，过滤空语句和注释
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== '\n');
    
    console.log(`📝 找到 ${sqlStatements.length} 条 SQL 语句`);
    
    // 逐条执行 SQL 语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      // 跳过纯注释行
      if (statement.startsWith('--') || statement.trim() === '') {
        continue;
      }
      
      try {
        console.log(`⚡ 执行第 ${i + 1} 条语句...`);
        console.log(`   ${statement.substring(0, 100)}${statement.length > 100 ? '...' : ''}`);
        
        await db.$executeRawUnsafe(statement);
        console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        
      } catch (error) {
        console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error);
        console.error(`   语句内容: ${statement}`);
        // 继续执行其他语句，不中断整个过程
      }
    }
    
  } catch (error) {
    console.error('❌ 读取或执行 SQL 文件失败:', error);
    throw error;
  }
}

/**
 * 验证注释是否添加成功
 */
async function verifyComments(): Promise<void> {
  try {
    console.log('\n🔍 验证表注释...');
    
    // 查询表注释
    const tableComment = await db.$queryRaw<Array<{table_comment: string}>>`
      SELECT 
        obj_description(oid) as table_comment
      FROM pg_class
      WHERE relname = 'SearchConfig'
    `;
    
    if (tableComment.length > 0 && tableComment[0].table_comment) {
      console.log('✅ 表注释添加成功:');
      console.log(`   ${tableComment[0].table_comment.substring(0, 200)}...`);
    } else {
      console.log('⚠️  未找到表注释');
    }
    
    console.log('\n🔍 验证字段注释...');
    
    // 查询字段注释
    const columnComments = await db.$queryRaw<Array<{column_name: string, column_comment: string}>>`
      SELECT 
        pa.attname as column_name,
        col_description(pgc.oid, pa.attnum) as column_comment
      FROM pg_attribute pa
      JOIN pg_class pgc ON pgc.oid = pa.attrelid
      WHERE pgc.relname = 'SearchConfig' 
        AND pa.attnum > 0 
        AND NOT pa.attisdropped
        AND col_description(pgc.oid, pa.attnum) IS NOT NULL
      ORDER BY pa.attnum
    `;
    
    if (columnComments.length > 0) {
      console.log(`✅ 成功为 ${columnComments.length} 个字段添加了注释:`);
      columnComments.forEach(comment => {
        console.log(`   - ${comment.column_name}: ${comment.column_comment.substring(0, 100)}...`);
      });
    } else {
      console.log('⚠️  未找到字段注释');
    }
    
  } catch (error) {
    console.error('❌ 验证注释失败:', error);
  }
}

/**
 * 显示 SearchConfig 表的字段功能说明
 */
function displayFieldExplanations(): void {
  console.log('\n📋 SearchConfig 配置表字段功能说明：\n');
  
  const fieldExplanations = [
    {
      category: '🔑 基础信息字段',
      fields: [
        { name: 'id', desc: '主键 - 搜索配置的唯一标识符' },
        { name: 'code', desc: '配置代码 - 程序中引用的唯一标识，如"us_pmn_device_name"' },
        { name: 'name', desc: '显示名称 - 用户界面显示的友好名称' },
        { name: 'description', desc: '描述信息 - 详细说明配置用途和功能' }
      ]
    },
    {
      category: '⚙️ 配置类型字段',
      fields: [
        { name: 'configType', desc: '配置类型 - SIMPLE_FILTER(简单过滤)/MULTI_FIELD(多字段)/CROSS_TABLE(跨表)/CUSTOM_LOGIC(自定义)' },
        { name: 'targetDatabases', desc: '目标数据库 - JSON数组，指定适用的数据库代码' },
        { name: 'searchFields', desc: '搜索字段配置 - JSON格式，根据配置类型有不同结构' }
      ]
    },
    {
      category: '🎨 UI界面字段',
      fields: [
        { name: 'displayOrder', desc: '显示顺序 - 控制在界面中的排序位置' },
        { name: 'filterType', desc: '组件类型 - input/select/multi_select/date_range/checkbox/range' },
        { name: 'placeholder', desc: '占位符 - 输入框提示文本' }
      ]
    },
    {
      category: '🔧 高级配置字段',
      fields: [
        { name: 'customLogic', desc: '自定义逻辑 - SQL片段或函数名，用于复杂搜索' },
        { name: 'validationRules', desc: '验证规则 - JSON格式的输入验证配置' },
        { name: 'options', desc: '选项配置 - 用于select类型的选项数据' }
      ]
    },
    {
      category: '🔐 权限状态字段',
      fields: [
        { name: 'accessLevel', desc: '访问权限 - free/premium/enterprise，控制功能分级' },
        { name: 'isActive', desc: '激活状态 - 控制配置是否启用' },
        { name: 'isAdvanced', desc: '高级功能 - 标识是否为高级搜索功能' }
      ]
    },
    {
      category: '⏰ 时间戳字段',
      fields: [
        { name: 'createdAt', desc: '创建时间 - 自动记录创建时间戳' },
        { name: 'updatedAt', desc: '更新时间 - 自动记录最后更新时间' }
      ]
    }
  ];
  
  fieldExplanations.forEach(category => {
    console.log(category.category);
    category.fields.forEach(field => {
      console.log(`  • ${field.name.padEnd(16)} - ${field.desc}`);
    });
    console.log('');
  });
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 开始为 SearchConfig 表添加注释...\n');
    
    // 显示字段功能说明
    displayFieldExplanations();
    
    // 获取 SQL 文件路径
    const sqlFilePath = path.join(__dirname, 'add-searchconfig-comments.sql');
    
    // 检查 SQL 文件是否存在
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`SQL 文件不存在: ${sqlFilePath}`);
    }
    
    // 执行 SQL 文件
    await executeSqlFile(sqlFilePath);
    
    // 验证注释是否添加成功
    await verifyComments();
    
    console.log('\n✅ SearchConfig 表注释添加完成！');
    console.log('\n💡 注释说明：');
    console.log('   - 表注释：解释了搜索配置表的整体功能和使用场景');
    console.log('   - 字段注释：详细说明了每个字段的作用和配置方法');
    console.log('   - 索引注释：说明了各个索引的用途和性能优化作用');
    console.log('\n📖 您现在可以在数据库管理工具中查看这些注释，');
    console.log('   或者使用 SQL 查询来获取字段说明信息。');
    
  } catch (error) {
    console.error('❌ 添加注释失败:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// 执行主函数
if (require.main === module) {
  main();
}
