-- 为 SearchConfig 表和字段添加注释说明
-- 这些注释将帮助开发者理解搜索配置系统的各个字段功能和用途

-- 1. 为表添加注释
COMMENT ON TABLE "SearchConfig" IS '搜索配置表 - 统一管理复杂搜索场景的配置，支持简单过滤、多字段搜索、跨表搜索和自定义逻辑等多种搜索模式';

-- 2. 为各个字段添加详细注释

-- 基础信息字段
COMMENT ON COLUMN "SearchConfig"."id" IS '主键 - 搜索配置的唯一标识符，使用UUID格式';
COMMENT ON COLUMN "SearchConfig"."code" IS '配置代码 - 搜索配置的唯一代码标识，如"product_name_search"、"us_pmn_device_name"等，用于程序中引用特定搜索配置';
COMMENT ON COLUMN "SearchConfig"."name" IS '显示名称 - 在用户界面中显示的友好名称，如"产品名称搜索"、"设备名称(美国PMN)"等';
COMMENT ON COLUMN "SearchConfig"."description" IS '描述信息 - 详细说明此搜索配置的用途、适用场景和功能特点，帮助管理员理解配置目的';

-- 配置类型和目标字段
COMMENT ON COLUMN "SearchConfig"."configType" IS '配置类型 - 搜索配置的类型枚举：SIMPLE_FILTER(简单过滤器，单字段搜索)、MULTI_FIELD(多字段搜索，同一数据库多个字段)、CROSS_TABLE(跨表搜索，多个数据库联合搜索)、CUSTOM_LOGIC(自定义逻辑搜索)';
COMMENT ON COLUMN "SearchConfig"."targetDatabases" IS '目标数据库列表 - JSON数组格式，指定此搜索配置适用的数据库代码，如["us_pmn", "us_class"]，支持单个或多个数据库';

-- 搜索字段配置
COMMENT ON COLUMN "SearchConfig"."searchFields" IS '搜索字段配置 - JSON格式的复杂配置对象，根据configType不同有不同结构：
- SIMPLE_FILTER: {"database": "us_pmn", "field": "devicename", "searchType": "contains"}
- MULTI_FIELD: {"fields": ["field1", "field2"], "operator": "OR", "searchType": "contains"}  
- CROSS_TABLE: {"mappings": [{"database": "us_pmn", "field": "productcode"}, {"database": "us_class", "field": "productcode"}], "searchType": "exact"}
- CUSTOM_LOGIC: 自定义结构，配合customLogic字段使用';

-- UI配置字段
COMMENT ON COLUMN "SearchConfig"."displayOrder" IS '显示顺序 - 控制搜索配置在用户界面中的显示顺序，数值越小越靠前，用于排序搜索选项';
COMMENT ON COLUMN "SearchConfig"."filterType" IS '前端组件类型 - 指定在用户界面中使用的输入组件类型：input(文本输入框)、select(下拉选择)、multi_select(多选下拉)、date_range(日期范围选择器)、checkbox(复选框)、range(数值范围选择器)';
COMMENT ON COLUMN "SearchConfig"."placeholder" IS '占位符文本 - 在输入框中显示的提示文本，如"请输入设备名称..."、"Enter device name..."等，提升用户体验';

-- 高级配置字段
COMMENT ON COLUMN "SearchConfig"."customLogic" IS '自定义搜索逻辑 - 当configType为CUSTOM_LOGIC时使用，可以是SQL片段、函数名或特殊逻辑标识符，用于实现复杂的搜索需求';
COMMENT ON COLUMN "SearchConfig"."validationRules" IS '验证规则 - JSON格式的输入验证配置，如{"required": true, "minLength": 2, "maxLength": 100, "pattern": "^[a-zA-Z0-9]+$"}，用于前端和后端数据验证';
COMMENT ON COLUMN "SearchConfig"."options" IS '选项配置 - JSON格式的额外配置选项，主要用于select和multi_select类型，如{"options": [{"value": "class1", "label": "一类器械"}, {"value": "class2", "label": "二类器械"}]}，也可存储其他自定义配置';

-- 权限和状态字段
COMMENT ON COLUMN "SearchConfig"."accessLevel" IS '访问权限级别 - 控制搜索功能的访问权限：free(免费用户可用)、premium(付费用户可用)、enterprise(企业用户可用)，用于功能分级';
COMMENT ON COLUMN "SearchConfig"."isActive" IS '激活状态 - 控制此搜索配置是否启用：true(启用，用户可见可用)、false(禁用，用户不可见)，用于功能开关控制';
COMMENT ON COLUMN "SearchConfig"."isAdvanced" IS '高级功能标识 - 标识是否为高级搜索功能：true(高级功能，可能在高级搜索界面显示)、false(基础功能，在基础搜索界面显示)，用于界面功能分组';

-- 时间戳字段
COMMENT ON COLUMN "SearchConfig"."createdAt" IS '创建时间 - 记录此搜索配置的创建时间戳，自动设置为当前时间';
COMMENT ON COLUMN "SearchConfig"."updatedAt" IS '更新时间 - 记录此搜索配置的最后更新时间戳，每次修改时自动更新';

-- 3. 为索引添加注释说明
COMMENT ON INDEX "SearchConfig_code_key" IS '配置代码唯一索引 - 确保每个搜索配置代码的唯一性，用于快速查找特定配置';
COMMENT ON INDEX "SearchConfig_configType_idx" IS '配置类型索引 - 用于按配置类型快速筛选搜索配置，提升查询性能';
COMMENT ON INDEX "SearchConfig_isActive_idx" IS '激活状态索引 - 用于快速筛选启用的搜索配置，避免全表扫描';
COMMENT ON INDEX "SearchConfig_displayOrder_idx" IS '显示顺序索引 - 用于按显示顺序快速排序搜索配置，提升界面加载性能';

-- 4. 更新表注释，添加详细的使用说明
COMMENT ON TABLE "SearchConfig" IS '搜索配置表 - 统一管理复杂搜索场景的配置，支持简单过滤、多字段搜索、跨表搜索和自定义逻辑等多种搜索模式

使用场景说明：
1. 简单搜索(SIMPLE_FILTER)：单个字段的精确或模糊搜索，如按设备名称搜索
2. 多字段搜索(MULTI_FIELD)：同一数据库中多个字段的组合搜索，支持AND/OR逻辑
3. 跨表搜索(CROSS_TABLE)：多个数据库表之间的关联搜索，如通过产品代码关联不同数据库
4. 自定义逻辑(CUSTOM_LOGIC)：复杂的业务逻辑搜索，通过customLogic字段实现

配置示例：
- 设备名称搜索：{"database": "us_pmn", "field": "devicename", "searchType": "contains"}
- 多字段搜索：{"fields": ["devicename", "companyname"], "operator": "OR", "searchType": "contains"}
- 跨表搜索：{"mappings": [{"database": "us_pmn", "field": "productcode"}, {"database": "us_class", "field": "productcode"}]}';

-- 5. 验证注释是否添加成功
SELECT 
  schemaname,
  tablename,
  obj_description(oid) as table_comment
FROM pg_tables pt
JOIN pg_class pc ON pc.relname = pt.tablename
WHERE pt.tablename = 'SearchConfig';

-- 查看字段注释
SELECT 
  column_name,
  col_description(pgc.oid, pa.attnum) as column_comment
FROM pg_attribute pa
JOIN pg_class pgc ON pgc.oid = pa.attrelid
JOIN information_schema.columns isc ON isc.column_name = pa.attname 
WHERE pgc.relname = 'SearchConfig' 
  AND pa.attnum > 0 
  AND NOT pa.attisdropped
  AND isc.table_name = 'SearchConfig'
ORDER BY pa.attnum;
